<?php $__env->startSection('title', 'ClickUp Settings'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">ClickUp Settings</h1>
        <div class="flex space-x-3">
            <a href="<?php echo e(route('clickup.dashboard')); ?>"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-md p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-green-700 dark:text-green-300"><?php echo e(session('success')); ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if($errors->any()): ?>
        <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800 dark:text-red-200">There were errors with your submission:</h3>
                    <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                        <ul class="list-disc pl-5 space-y-1">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Tabbed Interface -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
        <!-- Tab Navigation -->
        <div class="border-b border-gray-200 dark:border-gray-700">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                <button type="button" 
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-150 ease-in-out border-blue-500 text-blue-600 dark:text-blue-400" 
                        data-tab="workspace"
                        aria-selected="true">
                    <i class="fas fa-globe mr-2"></i>
                    Workspace
                </button>
                <button type="button" 
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-150 ease-in-out border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600" 
                        data-tab="teams"
                        aria-selected="false">
                    <i class="fas fa-users mr-2"></i>
                    Teams
                </button>
                <button type="button" 
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-150 ease-in-out border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600" 
                        data-tab="lists"
                        aria-selected="false">
                    <i class="fas fa-list mr-2"></i>
                    Lists
                </button>
                <button type="button" 
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-150 ease-in-out border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600" 
                        data-tab="general"
                        aria-selected="false">
                    <i class="fas fa-cog mr-2"></i>
                    General
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-6">
            <!-- Workspace Tab -->
            <div id="workspace-tab" class="tab-content">
                <?php echo $__env->make('plugins.clickup::settings.tabs.workspace', [
                    'currentToken' => $currentToken,
                    'apiStatus' => $apiStatus,
                    'workspaces' => $workspaces,
                    'selectedWorkspace' => $selectedWorkspace,
                    'spaces' => $spaces
                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>

            <!-- Teams Tab -->
            <div id="teams-tab" class="tab-content hidden">
                <?php echo $__env->make('plugins.clickup::settings.tabs.teams', [
                    'teams' => $teams,
                    'currentToken' => $currentToken
                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>

            <!-- Lists Tab -->
            <div id="lists-tab" class="tab-content hidden">
                <?php echo $__env->make('plugins.clickup::settings.tabs.lists', [
                    'lists' => $lists,
                    'assignedLists' => $assignedLists,
                    'currentToken' => $currentToken
                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>

            <!-- General Tab -->
            <div id="general-tab" class="tab-content hidden">
                <?php echo $__env->make('plugins.clickup::settings.tabs.general', [
                    'syncSettings' => $syncSettings,
                    'generalSettings' => $generalSettings,
                    'currentToken' => $currentToken
                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            
            // Remove active state from all buttons
            tabButtons.forEach(btn => {
                btn.classList.remove('border-blue-500', 'text-blue-600', 'dark:text-blue-400');
                btn.classList.add('border-transparent', 'text-gray-500', 'dark:text-gray-400');
                btn.setAttribute('aria-selected', 'false');
            });
            
            // Add active state to clicked button
            this.classList.remove('border-transparent', 'text-gray-500', 'dark:text-gray-400');
            this.classList.add('border-blue-500', 'text-blue-600', 'dark:text-blue-400');
            this.setAttribute('aria-selected', 'true');
            
            // Hide all tab contents
            tabContents.forEach(content => {
                content.classList.add('hidden');
            });
            
            // Show selected tab content
            const selectedTab = document.getElementById(tabName + '-tab');
            if (selectedTab) {
                selectedTab.classList.remove('hidden');
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/clickup/Views/settings/unified.blade.php ENDPATH**/ ?>