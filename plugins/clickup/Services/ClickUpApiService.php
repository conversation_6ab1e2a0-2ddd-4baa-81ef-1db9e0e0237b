<?php

namespace Plugins\ClickUp\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Plugins\ClickUp\Models\ClickUpApiToken;

class ClickUpApiService
{
    private const BASE_URL = 'https://api.clickup.com/api/v2';
    private const RATE_LIMIT_CACHE_KEY = 'clickup_rate_limit';
    private const DEFAULT_TIMEOUT = 30;

    private ?ClickUpApiToken $token = null;

    public function __construct(?ClickUpApiToken $token = null)
    {
        $this->token = $token ?? ClickUpApiToken::getActiveToken();
    }

    /**
     * Check if API is configured and ready
     */
    public function isConfigured(): bool
    {
        return $this->token && $this->token->isValid();
    }

    /**
     * Check if API is configured for testing (allows inactive tokens)
     */
    public function isConfiguredForTesting(): bool
    {
        return $this->token && $this->token->hasValidTokenFormat();
    }

    /**
     * Test the API connection
     */
    public function testConnection(): array
    {
        // For testing, we allow inactive tokens as long as they have valid format
        if (!$this->isConfiguredForTesting()) {
            return [
                'success' => false,
                'message' => 'No valid API token configured'
            ];
        }

        try {
            $response = $this->makeRequest('GET', '/user');
            
            if ($response['success']) {
                return [
                    'success' => true,
                    'message' => 'Connection successful',
                    'user' => $response['data']
                ];
            }

            return [
                'success' => false,
                'message' => $response['message'] ?? 'Connection failed'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get user information
     */
    public function getUser(): array
    {
        return $this->makeRequest('GET', '/user');
    }

    /**
     * Get all teams (workspaces)
     */
    public function getTeams(): array
    {
        return $this->makeRequest('GET', '/team');
    }

    /**
     * Get spaces for a team
     */
    public function getSpaces(string $teamId): array
    {
        return $this->makeRequest('GET', "/team/{$teamId}/space");
    }

    /**
     * Get folders in a space
     */
    public function getFolders(string $spaceId): array
    {
        return $this->makeRequest('GET', "/space/{$spaceId}/folder");
    }

    /**
     * Get lists in a space (folderless lists)
     */
    public function getListsInSpace(string $spaceId): array
    {
        return $this->makeRequest('GET', "/space/{$spaceId}/list");
    }

    /**
     * Get lists in a space
     */
    public function getLists(string $spaceId, bool $archived = false): array
    {
        $params = ['archived' => $archived ? 'true' : 'false'];
        return $this->makeRequest('GET', "/space/{$spaceId}/list", $params);
    }

    /**
     * Get lists in a folder
     */
    public function getListsInFolder(string $folderId, bool $archived = false): array
    {
        $params = ['archived' => $archived ? 'true' : 'false'];
        return $this->makeRequest('GET', "/folder/{$folderId}/list", $params);
    }

    /**
     * Get a specific list
     */
    public function getList(string $listId): array
    {
        return $this->makeRequest('GET', "/list/{$listId}");
    }

    /**
     * Get tasks in a list
     */
    public function getTasks(string $listId, array $params = []): array
    {
        $defaultParams = [
            'archived' => 'false',
            'include_closed' => 'true',
            'page' => 0
        ];

        $params = array_merge($defaultParams, $params);
        return $this->makeRequest('GET', "/list/{$listId}/task", $params);
    }

    /**
     * Get a specific task
     */
    public function getTask(string $taskId): array
    {
        return $this->makeRequest('GET', "/task/{$taskId}");
    }

    /**
     * Get task time tracking
     */
    public function getTaskTimeTracking(string $taskId): array
    {
        return $this->makeRequest('GET', "/task/{$taskId}/time");
    }

    /**
     * Make HTTP request to ClickUp API
     */
    private function makeRequest(string $method, string $endpoint, array $params = []): array
    {
        // For testing purposes, allow inactive tokens with valid format
        if (!$this->isConfigured() && !$this->isConfiguredForTesting()) {
            return [
                'success' => false,
                'message' => 'API not configured'
            ];
        }

        // Check rate limiting
        if ($this->token->isRateLimited()) {
            return [
                'success' => false,
                'message' => 'Rate limit exceeded. Please try again later.',
                'rate_limited' => true
            ];
        }

        try {
            $url = self::BASE_URL . $endpoint;
            
            $request = Http::timeout(self::DEFAULT_TIMEOUT)
                ->withHeaders([
                    'Authorization' => $this->token->token,
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'Business-App-ClickUp-Integration/1.0'
                ]);

            // Add query parameters for GET requests
            if ($method === 'GET' && !empty($params)) {
                $request = $request->withQueryParameters($params);
            }

            $response = $request->send($method, $url, $method !== 'GET' ? $params : []);

            // Update rate limit information
            $this->updateRateLimit($response);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status_code' => $response->status()
                ];
            }

            // Handle specific error cases
            $errorData = $response->json();
            $errorMessage = $this->parseErrorMessage($errorData, $response->status());

            Log::warning('ClickUp API request failed', [
                'method' => $method,
                'endpoint' => $endpoint,
                'status' => $response->status(),
                'error' => $errorData
            ]);

            return [
                'success' => false,
                'message' => $errorMessage,
                'status_code' => $response->status(),
                'error_data' => $errorData
            ];

        } catch (\Exception $e) {
            Log::error('ClickUp API request exception', [
                'method' => $method,
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Request failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update rate limit information from response headers
     */
    private function updateRateLimit($response): void
    {
        $remaining = $response->header('X-RateLimit-Remaining');
        $resetTime = $response->header('X-RateLimit-Reset');

        if ($remaining !== null && $resetTime !== null) {
            $resetAt = new \DateTime('@' . $resetTime);
            $this->token->updateRateLimit((int)$remaining, $resetAt);
        }
    }

    /**
     * Parse error message from API response
     */
    private function parseErrorMessage(array $errorData, int $statusCode): string
    {
        // Handle common error cases
        switch ($statusCode) {
            case 401:
                return 'Invalid or expired API token';
            case 403:
                return 'Access forbidden. Check your permissions.';
            case 404:
                return 'Resource not found';
            case 429:
                return 'Rate limit exceeded. Please try again later.';
            case 500:
                return 'ClickUp server error. Please try again later.';
            default:
                // Try to extract error message from response
                if (isset($errorData['err'])) {
                    return $errorData['err'];
                }
                if (isset($errorData['error'])) {
                    return $errorData['error'];
                }
                if (isset($errorData['message'])) {
                    return $errorData['message'];
                }
                return "API request failed with status {$statusCode}";
        }
    }

    /**
     * Get cached data or fetch from API
     */
    public function getCachedOrFetch(string $cacheKey, callable $fetchCallback, int $ttl = 1800): array
    {
        return Cache::remember($cacheKey, $ttl, $fetchCallback);
    }

    /**
     * Clear cache for specific keys
     */
    public function clearCache(array $keys): void
    {
        foreach ($keys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * Get API usage statistics
     */
    public function getApiUsageStats(): array
    {
        if (!$this->token) {
            return [
                'configured' => false,
                'rate_limit_remaining' => null,
                'rate_limit_reset_at' => null,
                'last_used_at' => null
            ];
        }

        return [
            'configured' => true,
            'rate_limit_remaining' => $this->token->rate_limit_remaining,
            'rate_limit_reset_at' => $this->token->rate_limit_reset_at,
            'last_used_at' => $this->token->last_used_at,
            'is_rate_limited' => $this->token->isRateLimited()
        ];
    }

    /**
     * Batch request helper for multiple API calls
     */
    public function batchRequests(array $requests): array
    {
        $results = [];

        foreach ($requests as $key => $request) {
            $method = $request['method'] ?? 'GET';
            $endpoint = $request['endpoint'];
            $params = $request['params'] ?? [];

            $results[$key] = $this->makeRequest($method, $endpoint, $params);

            // Add small delay between requests to respect rate limits
            if (count($requests) > 1) {
                usleep(100000); // 100ms delay
            }
        }

        return $results;
    }
}
