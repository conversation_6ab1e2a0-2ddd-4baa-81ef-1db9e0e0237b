@if(!$currentToken)
    <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    API Token Required
                </h3>
                <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                    <p>Please configure your ClickUp API token in the Workspace tab to manage lists.</p>
                </div>
            </div>
        </div>
    </div>
@else
    <!-- Lists Configuration -->
    <div class="mb-8">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Lists Configuration</h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">Configure and manage your ClickUp lists, including list organization and mapping to internal systems.</p>
        
        @if(!empty($lists))
            <!-- Lists Management -->
            <div class="space-y-6">
                <!-- Search and Filter -->
                <div class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" id="list-search" placeholder="Search lists..."
                               class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="flex space-x-2">
                        <select id="space-filter" class="border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Spaces</option>
                            @foreach($lists->groupBy('space_name') as $spaceName => $spaceList)
                                <option value="{{ $spaceName }}">{{ $spaceName }}</option>
                            @endforeach
                        </select>
                        <button type="button" 
                                class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                                onclick="refreshLists()">
                            <i class="fas fa-sync mr-2"></i>
                            Refresh
                        </button>
                    </div>
                </div>

                <!-- Lists Grid -->
                <div id="lists-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach($lists as $list)
                        <div class="list-item bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md transition duration-150 ease-in-out"
                             data-space="{{ $list['space_name'] ?? '' }}"
                             data-name="{{ strtolower($list['name']) }}">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex-1">
                                    <h4 class="text-lg font-medium text-gray-900 dark:text-white truncate">{{ $list['name'] }}</h4>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $list['space_name'] ?? 'Unknown Space' }}</p>
                                    <p class="text-xs text-gray-400 dark:text-gray-500">ID: {{ $list['id'] }}</p>
                                </div>
                                <div class="flex-shrink-0">
                                    @if(isset($list['status']) && $list['status'] === 'active')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                            Active
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                                            Inactive
                                        </span>
                                    @endif
                                </div>
                            </div>
                            
                            @if(isset($list['task_count']))
                                <div class="mb-3">
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        <i class="fas fa-tasks mr-1"></i>
                                        {{ $list['task_count'] }} tasks
                                    </p>
                                </div>
                            @endif
                            
                            <div class="flex space-x-2">
                                <button type="button" 
                                        class="flex-1 bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-3 rounded text-sm transition duration-150 ease-in-out"
                                        onclick="viewListDetails('{{ $list['id'] }}')">
                                    <i class="fas fa-eye mr-1"></i>
                                    View
                                </button>
                                <button type="button" 
                                        class="flex-1 bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-3 rounded text-sm transition duration-150 ease-in-out"
                                        onclick="assignList('{{ $list['id'] }}', '{{ $list['name'] }}')">
                                    <i class="fas fa-plus mr-1"></i>
                                    Assign
                                </button>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @else
            <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-8 text-center">
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-600 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-list text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Lists Found</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                        No lists were found in your selected workspace and spaces. Please check your workspace configuration.
                    </p>
                    <button type="button" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                            onclick="refreshLists()">
                        <i class="fas fa-sync mr-2"></i>
                        Refresh Lists
                    </button>
                </div>
            </div>
        @endif
    </div>

    <!-- Assigned Lists -->
    <div class="mb-8">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Assigned Lists</h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">Lists that are currently assigned to product managers or internal systems.</p>
        
        @if(!empty($assignedLists))
            <div class="space-y-3">
                @foreach($assignedLists as $assignedList)
                    <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
                                        <i class="fas fa-check text-green-600 dark:text-green-400 text-sm"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ $assignedList['name'] }}</h4>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        Assigned to: {{ $assignedList['assigned_to'] ?? 'System' }}
                                    </p>
                                </div>
                            </div>
                            <button type="button" 
                                    class="bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-600 text-white font-bold py-1 px-3 rounded text-sm transition duration-150 ease-in-out"
                                    onclick="unassignList('{{ $assignedList['id'] }}')">
                                <i class="fas fa-times mr-1"></i>
                                Unassign
                            </button>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6 text-center">
                <p class="text-sm text-gray-500 dark:text-gray-400">No lists are currently assigned.</p>
            </div>
        @endif
    </div>

    <!-- List Configuration Options -->
    <div class="mb-8">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">List Configuration</h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">Configure how lists are managed and synchronized.</p>
        
        <form method="POST" action="{{ route('clickup.settings.update-general') }}" class="space-y-6">
            @csrf
            <input type="hidden" name="section" value="lists">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="auto_sync_lists" value="1" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Auto-sync list changes</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Automatically sync when lists are modified in ClickUp</p>
                </div>
                
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="sync_list_members" value="1" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Sync list member assignments</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Keep list member assignments synchronized</p>
                </div>
                
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="track_list_performance" value="1" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Track list performance</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Enable performance tracking for lists</p>
                </div>
                
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="notify_list_changes" value="1" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Notify on list changes</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Send notifications when lists are modified</p>
                </div>
            </div>
            
            <div class="flex justify-end">
                <button type="submit"
                        class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-save mr-2"></i>
                    Save List Settings
                </button>
            </div>
        </form>
    </div>
@endif

<script>
// List search functionality
document.getElementById('list-search')?.addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const listItems = document.querySelectorAll('.list-item');
    
    listItems.forEach(item => {
        const name = item.getAttribute('data-name');
        if (name.includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});

// Space filter functionality
document.getElementById('space-filter')?.addEventListener('change', function() {
    const selectedSpace = this.value;
    const listItems = document.querySelectorAll('.list-item');
    
    listItems.forEach(item => {
        const space = item.getAttribute('data-space');
        if (!selectedSpace || space === selectedSpace) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});

function viewListDetails(listId) {
    // Implement list details view
    alert('List details feature coming soon!');
}

function assignList(listId, listName) {
    // Implement list assignment
    if (confirm(`Assign list "${listName}" to a product manager?`)) {
        // Make AJAX call to assign list
        fetch(`{{ route('clickup.settings.assign-lists') }}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                list_ids: [listId]
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to assign list: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error assigning list');
        });
    }
}

function unassignList(listId) {
    if (confirm('Are you sure you want to unassign this list?')) {
        // Make AJAX call to unassign list
        fetch(`{{ route('clickup.settings.unassign-list') }}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                list_id: listId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to unassign list: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error unassigning list');
        });
    }
}

function refreshLists() {
    location.reload();
}
</script>
