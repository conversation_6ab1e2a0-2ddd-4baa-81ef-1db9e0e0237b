<?php

namespace Plugins\ClickUp\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Validator;
use Plugins\ClickUp\Models\ClickUpApiToken;
use Plugins\ClickUp\Models\ClickUpProductManager;
use Plugins\ClickUp\Models\ClickUpList;
use Plugins\ClickUp\Services\ClickUpApiService;

class SettingsController extends Controller
{
    private ClickUpApiService $apiService;

    public function __construct(ClickUpApiService $apiService)
    {
        $this->apiService = $apiService;
    }

    /**
     * Display unified ClickUp settings with tabs
     */
    public function index(): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        // Get all data needed for the unified interface
        $currentToken = ClickUpApiToken::getActiveToken();
        $apiStatus = $this->apiService->getApiUsageStats();

        // Workspace data
        $workspaces = $this->getWorkspaces();
        $selectedWorkspace = $this->getSelectedWorkspace();
        $spaces = $selectedWorkspace ? $this->getSpaces($selectedWorkspace) : [];

        // Teams data
        $teams = $this->getTeams();

        // Lists data
        $lists = $this->getLists();
        $assignedLists = $this->getAssignedLists();

        // General settings
        $syncSettings = $this->getSyncSettings();
        $generalSettings = $this->getGeneralSettings();

        return view('plugins.clickup::settings.unified', compact(
            'currentToken',
            'apiStatus',
            'workspaces',
            'selectedWorkspace',
            'spaces',
            'teams',
            'lists',
            'assignedLists',
            'syncSettings',
            'generalSettings'
        ));
    }

    /**
     * Display legacy ClickUp settings (original interface)
     */
    public function legacy(): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $currentToken = ClickUpApiToken::getActiveToken();
        $apiStatus = $this->apiService->getApiUsageStats();
        $workspaces = $this->getWorkspaces();
        $selectedWorkspace = $this->getSelectedWorkspace();
        $spaces = $selectedWorkspace ? $this->getSpaces($selectedWorkspace) : [];

        return view('plugins.clickup::settings.legacy', compact('currentToken', 'apiStatus', 'workspaces', 'selectedWorkspace', 'spaces'));
    }

    /**
     * Display sync settings
     */
    public function sync(): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $currentToken = ClickUpApiToken::getActiveToken();
        $syncSettings = $this->getSyncSettings();

        return view('plugins.clickup::settings.sync', compact('currentToken', 'syncSettings'));
    }

    /**
     * Display general settings
     */
    public function general(): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $generalSettings = $this->getGeneralSettings();

        return view('plugins.clickup::settings.general', compact('generalSettings'));
    }

    /**
     * Display list assignments
     */
    public function assignments(): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $productManagers = ClickUpProductManager::with(['clickupLists'])->orderBy('name')->get();
        $unassignedLists = ClickUpList::whereNull('product_manager_id')->orderBy('name')->get();
        $allLists = ClickUpList::orderBy('name')->get();

        return view('plugins.clickup::settings.assignments', compact('productManagers', 'unassignedLists', 'allLists'));
    }

    /**
     * Save API token
     */
    public function saveToken(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $validator = Validator::make($request->all(), [
            'token' => 'required|string|min:10',
            'name' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Debug: Log the received token
            \Log::info('ClickUp Token Debug', [
                'raw_token' => $request->token,
                'token_length' => strlen($request->token),
                'token_trimmed' => trim($request->token),
                'trimmed_length' => strlen(trim($request->token))
            ]);

            // Create new token instance to test
            $testToken = new ClickUpApiToken([
                'name' => $request->name ?: 'Default Token',
                'is_active' => false,
                'created_by' => auth()->id()
            ]);

            // Trim the token to remove any whitespace
            $cleanToken = trim($request->token);
            $testToken->setTokenAttribute($cleanToken);

            // Validate token format first
            if (!$testToken->hasValidTokenFormat()) {
                \Log::error('ClickUp Token Validation Failed', [
                    'token' => $cleanToken,
                    'length' => strlen($cleanToken),
                    'format_check' => $testToken->hasValidTokenFormat(),
                    'retrieved_token' => $testToken->getTokenAttribute()
                ]);

                return redirect()->back()
                    ->withErrors(['token' => 'Invalid token format. ClickUp tokens should start with "pk_" followed by numbers and letters. Received: ' . substr($cleanToken, 0, 10) . '...'])
                    ->withInput();
            }

            // Test the token by making a simple API call
            $tempApiService = new ClickUpApiService($testToken);

            $testResponse = $tempApiService->testConnection();

            if (!$testResponse['success']) {
                return redirect()->back()
                    ->withErrors(['token' => 'Token validation failed: ' . $testResponse['message']])
                    ->withInput();
            }

            // Token is valid, save it
            $token = ClickUpApiToken::create([
                'name' => $request->name ?: 'Default Token',
                'encrypted_token' => $testToken->encrypted_token,
                'is_active' => true,
                'created_by' => auth()->id(),
                'metadata' => [
                    'user_info' => $testResponse['user'] ?? null,
                    'created_at' => now()->toISOString()
                ]
            ]);

            return redirect()->route('clickup.settings.index')
                ->with('success', 'ClickUp API token saved successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['token' => 'Failed to save token: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Delete API token
     */
    public function deleteToken(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $token = ClickUpApiToken::getActiveToken();

        if (!$token) {
            return redirect()->back()
                ->withErrors(['general' => 'No active token found.']);
        }

        try {
            $token->delete();

            return redirect()->route('clickup.settings.index')
                ->with('success', 'ClickUp API token deleted successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to delete token: ' . $e->getMessage()]);
        }
    }

    /**
     * Test API connection
     */
    public function testConnection(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $token = $request->input('token');

        if (!$token) {
            // Test existing token
            $result = $this->apiService->testConnection();
        } else {
            // Test provided token
            $testToken = new ClickUpApiToken([
                'name' => 'Test Token',
                'is_active' => false,
                'created_by' => auth()->id()
            ]);
            $testToken->setTokenAttribute($token);

            $tempApiService = new ClickUpApiService($testToken);
            
            $result = $tempApiService->testConnection();
        }

        return response()->json($result);
    }

    /**
     * Get API usage statistics
     */
    public function getApiStats(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_data')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $stats = $this->apiService->getApiUsageStats();
        
        return response()->json($stats);
    }

    /**
     * Update settings
     */
    public function updateSettings(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $validator = Validator::make($request->all(), [
            'sync_interval' => 'nullable|integer|min:300|max:86400', // 5 minutes to 24 hours
            'cache_duration' => 'nullable|integer|min:60|max:7200', // 1 minute to 2 hours
            'auto_sync' => 'boolean',
            'notifications_enabled' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Update settings in config or database
            // For now, we'll store in the token metadata
            $token = ClickUpApiToken::getActiveToken();
            
            if ($token) {
                $metadata = $token->metadata ?? [];
                $metadata['settings'] = [
                    'sync_interval' => $request->sync_interval ?? 3600,
                    'cache_duration' => $request->cache_duration ?? 1800,
                    'auto_sync' => $request->boolean('auto_sync', false),
                    'notifications_enabled' => $request->boolean('notifications_enabled', true),
                    'updated_at' => now()->toISOString(),
                    'updated_by' => auth()->id()
                ];
                
                $token->update(['metadata' => $metadata]);
            }

            return redirect()->route('clickup.settings.index')
                ->with('success', 'Settings updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to update settings: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Reset settings to defaults
     */
    public function resetSettings(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        try {
            $token = ClickUpApiToken::getActiveToken();
            
            if ($token) {
                $metadata = $token->metadata ?? [];
                $metadata['settings'] = [
                    'sync_interval' => 3600,
                    'cache_duration' => 1800,
                    'auto_sync' => false,
                    'notifications_enabled' => true,
                    'reset_at' => now()->toISOString(),
                    'reset_by' => auth()->id()
                ];
                
                $token->update(['metadata' => $metadata]);
            }

            return redirect()->route('clickup.settings.index')
                ->with('success', 'Settings reset to defaults successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to reset settings: ' . $e->getMessage()]);
        }
    }

    /**
     * Update sync settings
     */
    public function updateSyncSettings(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $validator = Validator::make($request->all(), [
            'sync_interval' => 'required|integer|min:300|max:86400',
            'auto_sync' => 'boolean',
            'sync_on_startup' => 'boolean',
            'batch_size' => 'required|integer|min:10|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $this->saveSyncSettings([
                'sync_interval' => $request->sync_interval,
                'auto_sync' => $request->boolean('auto_sync'),
                'sync_on_startup' => $request->boolean('sync_on_startup'),
                'batch_size' => $request->batch_size,
                'updated_at' => now()->toISOString(),
                'updated_by' => auth()->id()
            ]);

            return redirect()->route('clickup.settings.sync')
                ->with('success', 'Sync settings updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to update sync settings: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Update general settings
     */
    public function updateGeneralSettings(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $validator = Validator::make($request->all(), [
            'notifications_enabled' => 'boolean',
            'email_notifications' => 'boolean',
            'cache_duration' => 'required|integer|min:60|max:7200',
            'timezone' => 'required|string|max:50'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $this->saveGeneralSettings([
                'notifications_enabled' => $request->boolean('notifications_enabled'),
                'email_notifications' => $request->boolean('email_notifications'),
                'cache_duration' => $request->cache_duration,
                'timezone' => $request->timezone,
                'updated_at' => now()->toISOString(),
                'updated_by' => auth()->id()
            ]);

            return redirect()->route('clickup.settings.general')
                ->with('success', 'General settings updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to update general settings: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Get sync settings
     */
    private function getSyncSettings(): array
    {
        $token = ClickUpApiToken::getActiveToken();
        $defaults = [
            'sync_interval' => 3600,
            'auto_sync' => false,
            'sync_on_startup' => true,
            'batch_size' => 100
        ];

        if (!$token || !isset($token->metadata['sync_settings'])) {
            return $defaults;
        }

        return array_merge($defaults, $token->metadata['sync_settings']);
    }

    /**
     * Get general settings
     */
    private function getGeneralSettings(): array
    {
        $token = ClickUpApiToken::getActiveToken();
        $defaults = [
            'notifications_enabled' => true,
            'email_notifications' => false,
            'cache_duration' => 1800,
            'timezone' => 'UTC'
        ];

        if (!$token || !isset($token->metadata['general_settings'])) {
            return $defaults;
        }

        return array_merge($defaults, $token->metadata['general_settings']);
    }

    /**
     * Save sync settings
     */
    private function saveSyncSettings(array $settings): void
    {
        $token = ClickUpApiToken::getActiveToken();
        if ($token) {
            $metadata = $token->metadata ?? [];
            $metadata['sync_settings'] = $settings;
            $token->update(['metadata' => $metadata]);
        }
    }

    /**
     * Save general settings
     */
    private function saveGeneralSettings(array $settings): void
    {
        $token = ClickUpApiToken::getActiveToken();
        if ($token) {
            $metadata = $token->metadata ?? [];
            $metadata['general_settings'] = $settings;
            $token->update(['metadata' => $metadata]);
        }
    }

    /**
     * Assign lists to a product manager
     */
    public function assignLists(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $validator = Validator::make($request->all(), [
            'product_manager_id' => 'required|exists:click_up_product_managers,id',
            'list_ids' => 'required|array',
            'list_ids.*' => 'exists:click_up_lists,id'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $productManager = ClickUpProductManager::findOrFail($request->product_manager_id);

            // Assign the selected lists to this product manager
            ClickUpList::whereIn('id', $request->list_ids)
                ->update(['product_manager_id' => $productManager->id]);

            $assignedCount = count($request->list_ids);

            return redirect()->route('clickup.settings.assignments')
                ->with('success', "Successfully assigned {$assignedCount} lists to {$productManager->name}!");

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to assign lists: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Unassign a list from its product manager
     */
    public function unassignList(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $validator = Validator::make($request->all(), [
            'list_id' => 'required|exists:click_up_lists,id'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator);
        }

        try {
            $list = ClickUpList::findOrFail($request->list_id);
            $managerName = $list->productManager ? $list->productManager->name : 'Unknown';

            $list->update(['product_manager_id' => null]);

            return redirect()->route('clickup.settings.assignments')
                ->with('success', "Successfully unassigned '{$list->name}' from {$managerName}!");

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to unassign list: ' . $e->getMessage()]);
        }
    }

    /**
     * Get available workspaces
     */
    private function getWorkspaces(): array
    {
        if (!$this->apiService->isConfigured()) {
            return [];
        }

        try {
            $response = $this->apiService->getTeams();
            if ($response['success']) {
                return $response['data']['teams'] ?? [];
            }
        } catch (\Exception $e) {
            \Log::error('Failed to fetch ClickUp workspaces', ['error' => $e->getMessage()]);
        }

        return [];
    }

    /**
     * Get selected workspace from settings
     */
    private function getSelectedWorkspace(): ?string
    {
        $token = ClickUpApiToken::getActiveToken();
        if (!$token || !isset($token->metadata['selected_workspace'])) {
            return null;
        }

        return $token->metadata['selected_workspace'];
    }

    /**
     * Get spaces for selected workspace
     */
    private function getSpaces(?string $workspaceId): array
    {
        if (!$workspaceId || !$this->apiService->isConfigured()) {
            return [];
        }

        try {
            $response = $this->apiService->getSpaces($workspaceId);
            if ($response['success']) {
                return $response['data']['spaces'] ?? [];
            }
        } catch (\Exception $e) {
            \Log::error('Failed to fetch ClickUp spaces', ['error' => $e->getMessage()]);
        }

        return [];
    }

    /**
     * Update workspace and space selection
     */
    public function updateWorkspaceSettings(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $validator = Validator::make($request->all(), [
            'workspace_id' => 'required|string',
            'space_ids' => 'array',
            'space_ids.*' => 'string'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $token = ClickUpApiToken::getActiveToken();
            if ($token) {
                $metadata = $token->metadata ?? [];
                $metadata['selected_workspace'] = $request->workspace_id;
                $metadata['selected_spaces'] = $request->space_ids ?? [];
                $token->update(['metadata' => $metadata]);
            }

            return redirect()->route('clickup.settings.index')
                ->with('success', 'Workspace settings updated successfully! You can now sync data from the selected workspace.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to update workspace settings: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Get teams data for the unified interface
     */
    private function getTeams(): array
    {
        try {
            $token = ClickUpApiToken::getActiveToken();
            if (!$token) {
                return [];
            }

            $response = $this->apiService->getTeams();
            return $response['success'] ? ($response['data']['teams'] ?? []) : [];
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get lists data for the unified interface
     */
    private function getLists(): array
    {
        try {
            $selectedWorkspace = $this->getSelectedWorkspace();
            if (!$selectedWorkspace) {
                return [];
            }

            $spaces = $this->getSpaces($selectedWorkspace);
            $allLists = [];

            foreach ($spaces as $space) {
                $response = $this->apiService->getLists($space['id']);
                if ($response['success'] && isset($response['data']['lists'])) {
                    foreach ($response['data']['lists'] as $list) {
                        $list['space_name'] = $space['name'];
                        $allLists[] = $list;
                    }
                }
            }

            return $allLists;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get assigned lists for the unified interface
     */
    private function getAssignedLists(): array
    {
        try {
            // This would typically come from your database
            // For now, return empty array - implement based on your data model
            return [];
        } catch (\Exception $e) {
            return [];
        }
    }


}
