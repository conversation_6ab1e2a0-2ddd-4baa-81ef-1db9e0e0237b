<?php

use Illuminate\Support\Facades\Route;
use Plugins\ClickUp\Controllers\DashboardController;
use Plugins\ClickUp\Controllers\ListController;
use Plugins\ClickUp\Controllers\TaskController;
use Plugins\ClickUp\Controllers\ProductManagerController;
use Plugins\ClickUp\Controllers\ReportController;
use Plugins\ClickUp\Controllers\SettingsController;
use Plugins\ClickUp\Controllers\SyncController;

// Main ClickUp routes
Route::prefix('clickup')->name('clickup.')->group(function () {
    
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    
    // Settings routes (static paths first)
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [SettingsController::class, 'index'])->name('index');
        Route::get('/sync', [SettingsController::class, 'sync'])->name('sync');
        Route::get('/general', [SettingsController::class, 'general'])->name('general');
        Route::get('/assignments', [SettingsController::class, 'assignments'])->name('assignments');
        Route::post('/token', [SettingsController::class, 'saveToken'])->name('save-token');
        Route::delete('/token', [SettingsController::class, 'deleteToken'])->name('delete-token');
        Route::post('/test-connection', [SettingsController::class, 'testConnection'])->name('test-connection');
        Route::post('/update-sync', [SettingsController::class, 'updateSyncSettings'])->name('update-sync');
        Route::post('/update-general', [SettingsController::class, 'updateGeneralSettings'])->name('update-general');
        Route::post('/update-workspace', [SettingsController::class, 'updateWorkspaceSettings'])->name('update-workspace');
        Route::post('/assign-lists', [SettingsController::class, 'assignLists'])->name('assign-lists');
        Route::post('/unassign-list', [SettingsController::class, 'unassignList'])->name('unassign-list');
    });
    
    // Sync routes
    Route::prefix('sync')->name('sync.')->group(function () {
        Route::post('/lists', [SyncController::class, 'syncLists'])->name('lists');
        Route::post('/tasks', [SyncController::class, 'syncTasks'])->name('tasks');
        Route::post('/all', [SyncController::class, 'syncAll'])->name('all');
        Route::get('/status', [SyncController::class, 'status'])->name('status');
    });
    
    // Lists management
    Route::prefix('lists')->name('lists.')->group(function () {
        Route::get('/', [ListController::class, 'index'])->name('index');
        Route::get('/create', [ListController::class, 'create'])->name('create');
        Route::post('/', [ListController::class, 'store'])->name('store');
        Route::get('/{list}', [ListController::class, 'show'])->name('show');
        Route::get('/{list}/edit', [ListController::class, 'edit'])->name('edit');
        Route::put('/{list}', [ListController::class, 'update'])->name('update');
        Route::delete('/{list}', [ListController::class, 'destroy'])->name('destroy');
        Route::post('/{list}/assign-manager', [ListController::class, 'assignManager'])->name('assign-manager');
        Route::delete('/{list}/remove-manager', [ListController::class, 'removeManager'])->name('remove-manager');
    });
    
    // Tasks management
    Route::prefix('tasks')->name('tasks.')->group(function () {
        Route::get('/', [TaskController::class, 'index'])->name('index');
        Route::get('/{task}', [TaskController::class, 'show'])->name('show');
        Route::post('/{task}/sync', [TaskController::class, 'sync'])->name('sync');
    });
    
    // Product Managers
    Route::prefix('product-managers')->name('product-managers.')->group(function () {
        Route::get('/', [ProductManagerController::class, 'index'])->name('index');
        Route::get('/create', [ProductManagerController::class, 'create'])->name('create');
        Route::post('/', [ProductManagerController::class, 'store'])->name('store');
        Route::get('/{manager}', [ProductManagerController::class, 'show'])->name('show');
        Route::get('/{manager}/edit', [ProductManagerController::class, 'edit'])->name('edit');
        Route::put('/{manager}', [ProductManagerController::class, 'update'])->name('update');
        Route::delete('/{manager}', [ProductManagerController::class, 'destroy'])->name('destroy');
    });
    
    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('/performance', [ReportController::class, 'performance'])->name('performance');
        Route::get('/productivity', [ReportController::class, 'productivity'])->name('productivity');
        Route::get('/completion-rates', [ReportController::class, 'completionRates'])->name('completion-rates');
        Route::get('/time-tracking', [ReportController::class, 'timeTracking'])->name('time-tracking');
        Route::get('/bugs-features', [ReportController::class, 'bugsFeatures'])->name('bugs-features');
        Route::get('/export/{type}', [ReportController::class, 'export'])->name('export');
    });
    
    // API routes for AJAX calls
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('/lists', [ListController::class, 'apiIndex'])->name('lists');
        Route::get('/tasks', [TaskController::class, 'apiIndex'])->name('tasks');
        Route::get('/managers', [ProductManagerController::class, 'apiIndex'])->name('managers');
        Route::get('/reports/data', [ReportController::class, 'apiData'])->name('reports.data');
    });
});
